import { dbService } from './indexedDBService';
import { appUser } from './authService';

export interface UserSession {
  key: string;
  userId: number;
  username: string;
  token: string;
  identityToken: string;
  dbName: string;
  loginTime: number;
  expiresAt: number;
  isActive: boolean;
}

// Session duration: 7 days (in milliseconds)
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000;
const SESSION_KEY = 'current_user_session';

/**
 * Save user session to IndexedDB for persistent login
 */
export async function saveUserSession(
  userId: number,
  username: string,
  token: string,
  identityToken: string,
  dbName: string
): Promise<void> {
  try {
    await dbService.init();
    
    const now = Date.now();
    const session: UserSession = {
      key: SESSION_KEY,
      userId,
      username,
      token,
      identityToken,
      dbName,
      loginTime: now,
      expiresAt: now + SESSION_DURATION,
      isActive: true
    };

    await dbService.put('userSession', session);
    console.log('✅ User session saved successfully');
  } catch (error) {
    console.error('❌ Failed to save user session:', error);
    throw error;
  }
}

/**
 * Load user session from IndexedDB
 */
export async function loadUserSession(): Promise<UserSession | null> {
  try {
    await dbService.init();
    
    const session = await dbService.get<UserSession>('userSession', SESSION_KEY);
    
    if (!session) {
      console.log('📭 No saved user session found');
      return null;
    }

    // Check if session is expired
    const now = Date.now();
    if (now > session.expiresAt) {
      console.log('⏰ User session expired, removing...');
      await clearUserSession();
      return null;
    }

    // Check if session is active
    if (!session.isActive) {
      console.log('🚫 User session is inactive');
      return null;
    }

    console.log('✅ Valid user session found:', {
      username: session.username,
      userId: session.userId,
      dbName: session.dbName,
      expiresAt: new Date(session.expiresAt).toLocaleString()
    });

    return session;
  } catch (error) {
    console.error('❌ Failed to load user session:', error);
    return null;
  }
}

/**
 * Restore appUser state from saved session
 */
export async function restoreUserSession(): Promise<boolean> {
  try {
    const session = await loadUserSession();
    
    if (!session) {
      return false;
    }

    // Restore appUser state
    appUser.UserId = session.userId;
    appUser.userId = session.userId;
    appUser.UserName = session.username;
    appUser.Token = session.token;
    appUser.identityToken = session.identityToken;
    appUser.DbName = session.dbName;

    console.log('🔄 User session restored successfully:', {
      username: session.username,
      userId: session.userId,
      dbName: session.dbName
    });

    return true;
  } catch (error) {
    console.error('❌ Failed to restore user session:', error);
    return false;
  }
}

/**
 * Clear user session (logout)
 */
export async function clearUserSession(): Promise<void> {
  try {
    await dbService.init();
    
    // Mark session as inactive instead of deleting (for audit purposes)
    const session = await dbService.get<UserSession>('userSession', SESSION_KEY);
    if (session) {
      session.isActive = false;
      await dbService.put('userSession', session);
    }

    console.log('🗑️ User session cleared');
  } catch (error) {
    console.error('❌ Failed to clear user session:', error);
    throw error;
  }
}

/**
 * Update session expiry (extend session)
 */
export async function extendUserSession(): Promise<void> {
  try {
    await dbService.init();
    
    const session = await dbService.get<UserSession>('userSession', SESSION_KEY);
    if (session && session.isActive) {
      const now = Date.now();
      session.expiresAt = now + SESSION_DURATION;
      await dbService.put('userSession', session);
      
      console.log('⏰ User session extended until:', new Date(session.expiresAt).toLocaleString());
    }
  } catch (error) {
    console.error('❌ Failed to extend user session:', error);
  }
}

/**
 * Check if user has a valid session
 */
export async function hasValidSession(): Promise<boolean> {
  const session = await loadUserSession();
  return session !== null;
}

/**
 * Get current session info
 */
export async function getCurrentSessionInfo(): Promise<{
  isLoggedIn: boolean;
  username?: string;
  userId?: number;
  dbName?: string;
  expiresAt?: Date;
} | null> {
  try {
    const session = await loadUserSession();
    
    if (!session) {
      return { isLoggedIn: false };
    }

    return {
      isLoggedIn: true,
      username: session.username,
      userId: session.userId,
      dbName: session.dbName,
      expiresAt: new Date(session.expiresAt)
    };
  } catch (error) {
    console.error('❌ Failed to get session info:', error);
    return { isLoggedIn: false };
  }
}
