import React, { useState, useEffect } from 'react';
import { getCurrentSessionInfo } from '../services/persistentAuthService';

export function SessionDebugInfo() {
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const loadSessionInfo = async () => {
      try {
        const info = await getCurrentSessionInfo();
        setSessionInfo(info);
      } catch (error) {
        console.error('Failed to load session info:', error);
      }
    };

    loadSessionInfo();
    
    // Refresh session info every 10 seconds
    const interval = setInterval(loadSessionInfo, 10000);
    return () => clearInterval(interval);
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-700 z-50"
      >
        Session Info
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg z-50 max-w-sm">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold text-gray-800">Session Info</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      {sessionInfo ? (
        <div className="text-sm space-y-1">
          <div>
            <span className="font-medium">Status:</span>{' '}
            <span className={sessionInfo.isLoggedIn ? 'text-green-600' : 'text-red-600'}>
              {sessionInfo.isLoggedIn ? '✅ Logged In' : '❌ Not Logged In'}
            </span>
          </div>
          
          {sessionInfo.isLoggedIn && (
            <>
              <div>
                <span className="font-medium">Username:</span> {sessionInfo.username}
              </div>
              <div>
                <span className="font-medium">User ID:</span> {sessionInfo.userId}
              </div>
              <div>
                <span className="font-medium">Database:</span> {sessionInfo.dbName}
              </div>
              <div>
                <span className="font-medium">Expires:</span>{' '}
                <span className="text-xs">
                  {sessionInfo.expiresAt ? sessionInfo.expiresAt.toLocaleString() : 'Unknown'}
                </span>
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="text-sm text-gray-500">Loading...</div>
      )}
    </div>
  );
}
