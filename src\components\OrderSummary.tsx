import { TrashIcon, MinusIcon, PlusIcon, User, Phone, MapPin, Home, ShoppingBag, Percent, DollarSign } from 'lucide-react';
import { OrderItem, CustomerInfo, OrderType } from '../types';
import { formatPrice } from '../services/itemService';
import { useCurrency } from '../contexts/CurrencyContext';
import { dbService } from '../services/indexedDBService';
import { DiningTable } from '../types';
import { useState, useEffect } from 'react';

interface OrderSummaryProps {
  orderItems: OrderItem[];
  updateQuantity: (index: number, quantity: number) => void;
  removeItem: (index: number) => void;
  onCheckout: () => void;
  onClearOrder: () => void;
  customerInfo?: CustomerInfo;
  orderType?: OrderType;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  onCustomerInfoChange?: (customerInfo: CustomerInfo) => void;
  onOrderTypeChange?: (orderType: OrderType) => void;
  onDiscountChange?: (discount: number, discountType: 'percentage' | 'fixed') => void;
  selectedTableId?: string | null;
  onTableSelect?: () => void;
}

export function OrderSummary({
  orderItems,
  updateQuantity,
  removeItem,
  onCheckout,
  onClearOrder,
  customerInfo = { name: '', phone: '' },
  orderType = 'dine-in',
  discount = 0,
  discountType = 'percentage',
  onCustomerInfoChange,
  onOrderTypeChange,
  onDiscountChange,
  selectedTableId,
  onTableSelect
}: OrderSummaryProps) {
  const { defaultCurrency } = useCurrency();
  const [tableName, setTableName] = useState<string | null>(null);

  // Fetch table name when selectedTableId changes
  useEffect(() => {
    const fetchTableName = async () => {
      if (selectedTableId) {
        try {
          await dbService.init();
          const table = await dbService.get<DiningTable>('diningTables', selectedTableId);
          setTableName(table?.name || null);
        } catch (error) {
          console.error('Error fetching table name:', error);
          setTableName(null);
        }
      } else {
        setTableName(null);
      }
    };

    fetchTableName();
  }, [selectedTableId]);

  const calculateSubtotal = () => {
    return orderItems.reduce((total, item) => {
      const modifierPrice = item.modifierPrice || 0;
      return total + ((item.price || 0) + modifierPrice) * item.quantity * (1 - (item.discount || 0) / 100);
    }, 0);
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discountType === 'percentage') {
      return subtotal * (discount / 100);
    }
    return Math.min(discount, subtotal);
  };

  const calculateTaxableAmount = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  const calculateTax = () => {
    // return calculateTaxableAmount() * 0.0825; // 8.25% tax rate
    return orderItems.reduce((tax, item) => {
      const modifierPrice = item.modifierPrice || 0;
      return tax + (((item.price || 0) + modifierPrice) * item.quantity * (1 - (item.discount || 0) / 100) * (1 - 1 / (1 + item.vat / 100)));
    }, 0);
  };

  const calculateTotal = () => {
    return calculateTaxableAmount();// + calculateTax();
  };

  const handleCustomerInfoChange = (field: keyof CustomerInfo, value: string) => {
    const updatedInfo = { ...customerInfo, [field]: value };
    onCustomerInfoChange?.(updatedInfo);
  };

  const handleOrderTypeChange = (type: OrderType) => {
    onOrderTypeChange?.(type);
  };

  const handleDiscountChange = (newDiscount: number) => {
    onDiscountChange?.(newDiscount, discountType);
  };

  const handleDiscountTypeChange = (newDiscountType: 'percentage' | 'fixed') => {
    onDiscountChange?.(discount, newDiscountType);
  };

  const orderTypeIcons = {
    'dine-in': Home,
    'takeout': ShoppingBag,
    'delivery': MapPin
  };

  const orderTypeLabels = {
    'dine-in': 'Dine In',
    'takeout': 'Takeout',
    'delivery': 'Delivery'
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 bg-gray-800 text-white">
        <h2 className="text-xl font-bold">Current Order</h2>
      </div>

      {/* Order Type Selection */}
      <div className="p-4 bg-gray-100 border-b">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">Order Type</h3>
        <div className="grid grid-cols-3 gap-2">
          {(['dine-in', 'takeout', 'delivery'] as OrderType[]).map((type) => {
            const Icon = orderTypeIcons[type];
            return (
              <button
                key={type}
                onClick={() => handleOrderTypeChange(type)}
                className={`flex flex-col items-center p-2 rounded-lg border-2 transition-colors ${orderType === type
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-600 hover:border-gray-400'
                  }`}
              >
                <Icon size={20} className="mb-1" />
                <span className="text-xs font-medium">{orderTypeLabels[type]}</span>
              </button>
            );
          })}
        </div>

        {/* Table Selection for Dine-in */}
        {orderType === 'dine-in' && (
          <div className="mt-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Table:</span>
              {selectedTableId ? (
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-blue-700">
                    {tableName || selectedTableId}
                  </span>
                  <button
                    onClick={onTableSelect}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    Change
                  </button>
                </div>
              ) : (
                <button
                  onClick={onTableSelect}
                  className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                >
                  Select Table
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Customer Information */}
      <div className="p-4 bg-gray-50 border-b">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">Customer Information</h3>
        <div className="space-y-2">
          <div className="relative">
            <User size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Customer Name (Optional)"
              value={customerInfo.name}
              onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
          <div className="relative">
            <Phone size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="tel"
              placeholder="Phone Number (Optional)"
              value={customerInfo.phone}
              onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
        </div>
      </div>

      {/* Order Items */}
      {orderItems.length === 0 ? (
        <div > {/* className="flex-1 flex items-center justify-center p-6 text-gray-500" */}
          <p>No items in order</p>
        </div>
      ) : (
        <div > {/* className="flex-1 flex flex-col" */}
          {/* Order Items - Fixed height with scroll */}
          <div > {/* className="flex-1 overflow-y-auto p-4" */}
            {orderItems.map((item, index) => (
              <div key={index} className="mb-4 bg-white p-3 rounded-lg shadow-sm border">
                <div className="flex justify-between">
                  <div className="flex-1">
                    <p className="font-semibold text-gray-800">{item.name}</p>
                    <p className="text-sm text-gray-600">{formatPrice(item.price || 0, defaultCurrency || undefined)} each</p>
                    {item.modifiers && item.modifiers.length > 0 && (
                      <ul className="text-sm text-gray-600 ml-4 mt-1">
                        {item.modifiers.map(mod => (
                          <li key={mod.id}>
                            + {mod.name} ({formatPrice(mod.price, defaultCurrency || undefined)})
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-800">
                      {formatPrice(((item.price || 0) + (item.modifierPrice || 0)) * item.quantity * (1 - (item.discount || 0) / 100), defaultCurrency || undefined)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => updateQuantity(index, item.quantity - 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
                    >
                      <MinusIcon size={16} />
                    </button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <button
                      onClick={() => updateQuantity(index, item.quantity + 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
                    >
                      <PlusIcon size={16} />
                    </button>
                  </div>
                  <button
                    onClick={() => removeItem(index)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <TrashIcon size={18} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Discount Section */}
      {orderItems.length > 0 && (
        <div className="p-4 bg-gray-50 border-t border-b">
          <h3 className="text-sm font-semibold text-gray-700 mb-2">Discount</h3>
          <div className="flex space-x-2">
            <div className="flex-1">
              <input
                type="number"
                placeholder="0"
                value={discount || ''}
                onChange={(e) => handleDiscountChange(Number(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                min="0"
                step="0.01"
              />
            </div>
            <div className="flex rounded-lg border border-gray-300 overflow-hidden">
              <button
                onClick={() => handleDiscountTypeChange('percentage')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${discountType === 'percentage'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
              >
                <Percent size={16} />
              </button>
              <button
                onClick={() => handleDiscountTypeChange('fixed')}
                className={`px-3 py-2 text-sm font-medium transition-colors ${discountType === 'fixed'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
              >
                <DollarSign size={16} />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Order Summary */}
      <div className="border-t border-gray-200 p-4 bg-white">
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-gray-700">
            <span>Subtotal:</span>
            <span>{formatPrice(calculateSubtotal(), defaultCurrency || undefined)}</span>
          </div>
          {discount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>
                Discount ({discountType === 'percentage' ? `${discount}%` : formatPrice(discount, defaultCurrency || undefined)}):
              </span>
              <span>-{formatPrice(calculateDiscount(), defaultCurrency || undefined)}</span>
            </div>
          )}
          {/* <div className="flex justify-between text-gray-700">
            <span>Taxable Amount:</span>
            <span>{formatPrice(calculateTaxableAmount(), defaultCurrency || undefined)}</span>
          </div> */}
          <div className="flex justify-between text-gray-700">
            <span>Tax :</span>
            <span>{formatPrice(calculateTax(), defaultCurrency || undefined)}</span>
          </div>
          <div className="flex justify-between font-bold text-lg text-gray-800 pt-2 border-t">
            <span>Total:</span>
            <span>{formatPrice(calculateTotal(), defaultCurrency || undefined)}</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={onClearOrder}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors font-medium"
            disabled={orderItems.length === 0}
          >
            Clear Order
          </button>
          <button
            onClick={onCheckout}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            disabled={orderItems.length === 0}
          >
            Checkout
          </button>
        </div>
      </div>
    </div>
  );
}