import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Currency } from '../types';
import { getDefaultCurrency } from '../services/itemService';

interface CurrencyContextType {
  defaultCurrency: Currency | null;
  setDefaultCurrency: (currency: Currency | null) => void;
  isLoading: boolean;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

interface CurrencyProviderProps {
  children: ReactNode;
}

export function CurrencyProvider({ children }: CurrencyProviderProps) {
  const [defaultCurrency, setDefaultCurrency] = useState<Currency | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDefaultCurrency = async () => {
      try {
        const currency = await getDefaultCurrency();
        setDefaultCurrency(currency);
      } catch (error) {
        console.error('Failed to load default currency:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDefaultCurrency();
  }, []);

  return (
    <CurrencyContext.Provider value={{ defaultCurrency, setDefaultCurrency, isLoading }}>
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}
