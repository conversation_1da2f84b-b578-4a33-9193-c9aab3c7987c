// IndexedDB service for local data caching
export interface DBSchema {
  items: {
    key: number;
    value: any;
    indexes: { familyId: number; stmp: number };
  };
  families: {
    key: number;
    value: any;
    indexes: { stmp: number };
  };
  itemUnits: {
    key: number;
    value: any;
    indexes: { itemId: number; stmp: number };
  };
  unitPrices: {
    key: number;
    value: any;
    indexes: { itemUnitId: number; priceListId: number; stmp: number };
  };
  currencies: {
    key: number;
    value: any;
    indexes: { stmp: number };
  };
  options: {
    key: number;
    value: any;
  };
  orders: {
    key: string;
    value: any;
    indexes: { orderNumber: string; status: string; createdAt: number; customerName: string };
  };
  invoices: {
    key: string;
    value: any;
    indexes: { invoiceNumber: string; orderNumber: string; status: string; createdAt: number };
  };
  receipts: {
    key: string;
    value: any;
    indexes: { receiptNumber: string; invoiceId: string; createdAt: number };
  };
  diningTables: {
    key: number;
    value: any;
    indexes: { number: number; status: string; layoutId: string };
  };
  tableLayouts: {
    key: string;
    value: any;
    indexes: { name: string; isActive: boolean; createdAt: string };
  };
  metadata: {
    key: string;
    value: { lastSync: number; maxStmp: number };
  };
  userSession: {
    key: string;
    value: {
      userId: number;
      username: string;
      token: string;
      identityToken: string;
      dbName: string;
      loginTime: number;
      expiresAt: number;
      isActive: boolean;
    };
  };
}

class IndexedDBService {
  private db: IDBDatabase | null = null;
  private readonly dbName = 'ResturantPOSDB';
  private readonly dbVersion = 3;

  async init(): Promise<void> {
    if (this.db) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('Error opening IndexedDB:', request.error);
        reject(request.error);
      };
      request.onsuccess = () => {
        this.db = request.result;
        console.log('IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Items store
        if (!db.objectStoreNames.contains('items')) {
          const itemsStore = db.createObjectStore('items', { keyPath: 'id' });
          itemsStore.createIndex('familyId', 'familyId', { unique: false });
          itemsStore.createIndex('stmp', 'stmp', { unique: false });
        }

        // Families store
        if (!db.objectStoreNames.contains('families')) {
          const familiesStore = db.createObjectStore('families', { keyPath: 'id' });
          familiesStore.createIndex('stmp', 'stmp', { unique: false });
        }

        // Item Units store
        if (!db.objectStoreNames.contains('itemUnits')) {
          const itemUnitsStore = db.createObjectStore('itemUnits', { keyPath: 'id' });
          itemUnitsStore.createIndex('itemId', 'itemId', { unique: false });
          itemUnitsStore.createIndex('stmp', 'stmp', { unique: false });
        }

        // Unit Prices store
        if (!db.objectStoreNames.contains('unitPrices')) {
          const unitPricesStore = db.createObjectStore('unitPrices', { keyPath: 'id' });
          unitPricesStore.createIndex('itemUnitId', 'itemUnitId', { unique: false });
          unitPricesStore.createIndex('priceListId', 'priceListId', { unique: false });
          unitPricesStore.createIndex('stmp', 'stmp', { unique: false });
        }

        // Currencies store
        if (!db.objectStoreNames.contains('currencies')) {
          const currenciesStore = db.createObjectStore('currencies', { keyPath: 'id' });
          currenciesStore.createIndex('stmp', 'stmp', { unique: false });
        }

        // Options store
        if (!db.objectStoreNames.contains('options')) {
          db.createObjectStore('options', { keyPath: 'id' });
        }

        // Orders store
        if (!db.objectStoreNames.contains('orders')) {
          const ordersStore = db.createObjectStore('orders', { keyPath: 'id' });
          ordersStore.createIndex('orderNumber', 'orderNumber', { unique: true });
          ordersStore.createIndex('status', 'status', { unique: false });
          ordersStore.createIndex('createdAt', 'createdAt', { unique: false });
          ordersStore.createIndex('customerName', 'customerInfo.name', { unique: false });
        }

        // Invoices store
        if (!db.objectStoreNames.contains('invoices')) {
          const invoicesStore = db.createObjectStore('invoices', { keyPath: 'id' });
          invoicesStore.createIndex('invoiceNumber', 'invoiceNumber', { unique: true });
          invoicesStore.createIndex('orderNumber', 'orderNumber', { unique: false });
          invoicesStore.createIndex('status', 'status', { unique: false });
          invoicesStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Receipts store
        if (!db.objectStoreNames.contains('receipts')) {
          const receiptsStore = db.createObjectStore('receipts', { keyPath: 'id' });
          receiptsStore.createIndex('receiptNumber', 'receiptNumber', { unique: true });
          receiptsStore.createIndex('invoiceId', 'invoiceId', { unique: false });
          receiptsStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Dining Tables store (operational tables)
        if (!db.objectStoreNames.contains('diningTables')) {
          const diningTablesStore = db.createObjectStore('diningTables', { keyPath: 'id' });
          diningTablesStore.createIndex('number', 'number', { unique: false }); // Not unique as multiple layouts can have same numbers
          diningTablesStore.createIndex('status', 'status', { unique: false });
          diningTablesStore.createIndex('layoutId', 'layoutId', { unique: false });
        }

        // Table Layouts store (floor plan layouts)
        if (!db.objectStoreNames.contains('tableLayouts')) {
          const tableLayoutsStore = db.createObjectStore('tableLayouts', { keyPath: 'id' });
          tableLayoutsStore.createIndex('name', 'name', { unique: false });
          tableLayoutsStore.createIndex('isActive', 'isActive', { unique: false });
          tableLayoutsStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Metadata store
        if (!db.objectStoreNames.contains('metadata')) {
          db.createObjectStore('metadata', { keyPath: 'key' });
        }

        // User session store for persistent login
        if (!db.objectStoreNames.contains('userSession')) {
          db.createObjectStore('userSession', { keyPath: 'key' });
        }
      };
    });
  }

  async getAll<T>(storeName: keyof DBSchema): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async getByIndex<T>(storeName: keyof DBSchema, indexName: string, value: any): Promise<T[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db!.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);

        // Check if the index exists
        if (!store.indexNames.contains(indexName)) {
          console.error(`Index '${indexName}' does not exist in store '${storeName}'`);
          console.log(`Available indexes in '${storeName}':`, Array.from(store.indexNames));
          reject(new Error(`Index '${indexName}' does not exist in store '${storeName}'`));
          return;
        }

        const index = store.index(indexName);

        // Handle boolean values properly for IndexedDB
        let request: IDBRequest;
        if (typeof value === 'boolean') {
          // For boolean values, we need to use a key range or iterate through all values
          // Since we're looking for exact boolean matches, we can use the value directly
          // but we need to ensure it's properly handled
          request = index.getAll(IDBKeyRange.only(value));
        } else {
          request = index.getAll(value);
        }

        request.onerror = () => {
          console.error(`Error querying index '${indexName}' in store '${storeName}':`, request.error);

          // If boolean key range fails, try alternative approach
          if (typeof value === 'boolean') {
            console.log(`Boolean key range failed, trying alternative approach for ${indexName}=${value}`);
            try {
              const allRequest = index.getAll();
              allRequest.onsuccess = () => {
                const filtered = allRequest.result.filter((item: any) => item[indexName] === value);
                console.log(`Filtered results for ${indexName}=${value}:`, filtered);
                resolve(filtered);
              };
              allRequest.onerror = () => reject(allRequest.error);
              return;
            } catch (fallbackError) {
              console.error('Fallback approach also failed:', fallbackError);
            }
          }

          reject(request.error);
        };
        request.onsuccess = () => {
          console.log(`Successfully queried ${indexName}=${value}, results:`, request.result);
          resolve(request.result);
        };

        transaction.onerror = () => {
          console.error(`Transaction error for store '${storeName}':`, transaction.error);
          reject(transaction.error);
        };
      } catch (error) {
        console.error(`Error accessing store '${storeName}' or index '${indexName}':`, error);
        reject(error);
      }
    });
  }

  async bulkPut<T>(storeName: keyof DBSchema, data: T[]): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      transaction.onerror = () => reject(transaction.error);
      transaction.oncomplete = () => resolve();

      data.forEach(item => store.put(item));
    });
  }

  async put<T>(storeName: keyof DBSchema, data: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async get<T>(storeName: keyof DBSchema, key: any): Promise<T | undefined> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    });
  }

  async delete(storeName: keyof DBSchema, key: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async getMaxStmp(storeName: keyof DBSchema): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);

      if (!store.indexNames.contains('stmp')) {
        console.warn(`Store '${storeName}' does not have an 'stmp' index.`);
        resolve(0);
        return;
      }

      const index = store.index('stmp');
      const request = index.openCursor(null, 'prev');

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const cursor = request.result;
        if (cursor) {
          resolve(cursor.value.stmp);
        } else {
          resolve(0);
        }
      };
    });
  }

  // Function to get all unposted invoices from IndexedDB
  async getUnpostedInvoices(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['invoices'], 'readonly');
      const store = transaction.objectStore('invoices');
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const allInvoices = request.result;
        // Filter invoices where posted = false
        const unpostedInvoices = allInvoices.filter(invoice => invoice.posted === false);
        resolve(unpostedInvoices);
      };
    });
  }

  async updateInvoicePostedStatus(invoiceId: string, posted: boolean): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['invoices'], 'readwrite');
      const store = transaction.objectStore('invoices');
      const getRequest = store.get(invoiceId);

      getRequest.onsuccess = () => {
        const invoice = getRequest.result;
        if (invoice) {
          invoice.posted = posted;
          const updateRequest = store.put(invoice);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(updateRequest.error);
        } else {
          reject(new Error('Invoice not found'));
        }
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async clear(storeName: keyof DBSchema): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  // Debug method to check database schema
  async debugDatabaseSchema(): Promise<void> {
    if (!this.db) {
      console.log('Database not initialized');
      return;
    }

    console.log('Database name:', this.db.name);
    console.log('Database version:', this.db.version);
    console.log('Object stores:', Array.from(this.db.objectStoreNames));

    // Check each store and its indexes
    const transaction = this.db.transaction(Array.from(this.db.objectStoreNames), 'readonly');
    for (const storeName of this.db.objectStoreNames) {
      const store = transaction.objectStore(storeName);
      console.log(`Store '${storeName}' indexes:`, Array.from(store.indexNames));
    }
  }

  // Method to reset database (for debugging purposes)
  async resetDatabase(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }

    return new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(this.dbName);
      deleteRequest.onerror = () => reject(deleteRequest.error);
      deleteRequest.onsuccess = () => {
        console.log('Database deleted successfully');
        resolve();
      };
    });
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export const dbService = new IndexedDBService();
