
import { useState, useEffect } from 'react';
import { getInvoices, EnrichedInvoice } from '../services/invoiceService';
import { formatPrice } from '../services/itemService';
import { useCurrency } from '../contexts/CurrencyContext';
import { ReceiptModal } from '../components/ReceiptModal';
import { OrderItem } from '../types';

export function InvoicePage() {
  const [invoices, setInvoices] = useState<EnrichedInvoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<EnrichedInvoice | null>(null);
   const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const { defaultCurrency } = useCurrency();
const [showReceiptModal, setShowReceiptModal] = useState<boolean>(false);

  useEffect(() => {
    const fetchInvoices = async () => {
      const fetchedInvoices = await getInvoices();
      setInvoices(fetchedInvoices);
    };

    fetchInvoices();
  }, []);

  const handlePrint = () => {
    if (selectedInvoice) {
      setShowReceiptModal(true);
      // const printContent = `
      //   <html>
      //     <head>
      //       <title>Invoice #${selectedInvoice.invoiceNumber}</title>
      //       <style>
      //         body { font-family: sans-serif; }
      //         .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, .15); font-size: 16px; line-height: 24px; color: #555; }
      //         .invoice-box table { width: 100%; line-height: inherit; text-align: left; }
      //         .invoice-box table td { padding: 5px; vertical-align: top; }
      //         .invoice-box table tr td:nth-child(2) { text-align: right; }
      //         .invoice-box table tr.top table td { padding-bottom: 20px; }
      //         .invoice-box table tr.top table td.title { font-size: 45px; line-height: 45px; color: #333; }
      //         .invoice-box table tr.information table td { padding-bottom: 40px; }
      //         .invoice-box table tr.heading td { background: #eee; border-bottom: 1px solid #ddd; font-weight: bold; }
      //         .invoice-box table tr.details td { padding-bottom: 20px; }
      //         .invoice-box table tr.item td{ border-bottom: 1px solid #eee; }
      //         .invoice-box table tr.item.last td { border-bottom: none; }
      //         .invoice-box table tr.total td:nth-child(2) { border-top: 2px solid #eee; font-weight: bold; }
      //       </style>
      //     </head>
      //     <body>
      //       <div class="invoice-box">
      //         <table>
      //           <tr class="top">
      //             <td colspan="2">
      //               <table>
      //                 <tr>
      //                   <td class="title">
      //                     Your Restaurant
      //                   </td>
      //                   <td>
      //                     Invoice #${selectedInvoice.invoiceNumber}<br>
      //                     Created: ${new Date(selectedInvoice.createdAt).toLocaleDateString()}<br>
      //                   </td>
      //                 </tr>
      //               </table>
      //             </td>
      //           </tr>
      //           <tr class="information">
      //             <td colspan="2">
      //               <table>
      //                 <tr>
      //                   <td>
      //                     Customer: ${selectedInvoice.customer.name || 'N/A'}<br>
      //                     ${selectedInvoice.customer.phone || ''}
      //                   </td>
      //                 </tr>
      //               </table>
      //             </td>
      //           </tr>
      //           <tr class="heading">
      //             <td>Item</td>
      //             <td>Price</td>
      //           </tr>
      //           ${selectedInvoice.items.map(item => `
      //             <tr class="item">
      //               <td>${item.name} x ${item.quantity}</td>
      //               <td>${formatPrice(item.price || 0 * item.quantity, defaultCurrency || undefined)}</td>
      //             </tr>
      //           `).join('')}
      //           <tr class="total">
      //             <td></td>
      //             <td>Total: ${formatPrice(selectedInvoice.total || 0, defaultCurrency || undefined)}</td>
      //           </tr>
      //         </table>
      //       </div>
      //     </body>
      //   </html>
      // `;
      // const printWindow = window.open('', '', 'height=600,width=800');
      // if (printWindow) {
      //   printWindow.document.write(printContent);
      //   printWindow.document.close();
      //   printWindow.print();
      // }
    }
  };
  const clearOrder = () => {
    setShowReceiptModal(false);
  };
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Invoices</h1>
      <div className="bg-white shadow-md rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invoices.map(invoice => (
              <tr key={invoice.id} onDoubleClick={() => setCurrentOrder(invoice.items) } className='cursor-pointer'>
                <td className="px-6 py-4 whitespace-nowrap">{invoice.invoiceNumber}</td>
                <td className="px-6 py-4 whitespace-nowrap">{new Date(invoice.createdAt).toLocaleDateString()}</td>
                <td className="px-6 py-4 whitespace-nowrap">{invoice.customer.name || 'N/A'}</td>
                <td className="px-6 py-4 whitespace-nowrap">{formatPrice(invoice.total - invoice.tax, defaultCurrency || undefined)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-right">
                  <button onClick={() => {setCurrentOrder(invoice.items), setShowReceiptModal(true) }} className="text-blue-600 hover:text-blue-900">View</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* {selectedInvoice && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">Invoice #{selectedInvoice.invoiceNumber}</h3>
                <div className="mt-2">
                  <p><strong>Date:</strong> {new Date(selectedInvoice.createdAt).toLocaleDateString()}</p>
                  <p><strong>Customer:</strong> {selectedInvoice.customer.name || 'N/A'}</p>
                  <p><strong>Total:</strong> {formatPrice(selectedInvoice.total, defaultCurrency || undefined)}</p>
                  <h4 className="font-bold mt-4">Items:</h4>
                  <ul>
                    {selectedInvoice.items.map(item => (
                      <li key={item.id}>{item.name} x {item.quantity} - {formatPrice(item.price || 0 * item.quantity, defaultCurrency || undefined)}</li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button onClick={handlePrint} type="button" className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                  Print
                </button>
                <button onClick={() => {setCurrentOrder([]) }} type="button" className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )} */}
        {showReceiptModal && <ReceiptModal order={currentOrder} onClose={clearOrder} btnNewVisible={false} />}
    </div>
  );
}
