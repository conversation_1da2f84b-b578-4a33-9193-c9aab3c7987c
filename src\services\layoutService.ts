import { dbService } from './indexedDBService';
import { TableLayout, LayoutTable, DiningTable } from '../types';

// Initialize layout service
export async function initializeLayoutService(): Promise<void> {
  await dbService.init();
}

// Get all layouts
export async function getAllLayouts(): Promise<TableLayout[]> {
  await initializeLayoutService();
  return await dbService.getAll<TableLayout>('tableLayouts');
}

// Get layout by ID
export async function getLayoutById(layoutId: string): Promise<TableLayout | null> {
  await initializeLayoutService();
  const result = await dbService.get<TableLayout>('tableLayouts', layoutId);
  return result || null;
}

// Get active layout (deprecated - use getActiveLayouts for multiple layouts)
export async function getActiveLayout(): Promise<TableLayout | null> {
  await initializeLayoutService();
  // Use getAll and filter instead of getByIndex for boolean values
  const allLayouts = await dbService.getAll<TableLayout>('tableLayouts');
  const activeLayout = allLayouts.find(layout => layout.isActive === true);
  return activeLayout || null;
}

// Get all active layouts (supports multiple active layouts)
export async function getActiveLayouts(): Promise<TableLayout[]> {
  await initializeLayoutService();
  const allLayouts = await dbService.getAll<TableLayout>('tableLayouts');
  return allLayouts; //.filter(layout => layout.isActive === true);
}

// Save layout
export async function saveLayout(layout: TableLayout): Promise<void> {
  await initializeLayoutService();
  await dbService.put('tableLayouts', layout);
}

// Create new layout
export async function createLayout(
  name: string,
  tables: LayoutTable[],
  floorPlanImage: string | null = null
): Promise<TableLayout> {
  const layout: TableLayout = {
    id: Date.now().toString(),
    name,
    tables,
    floorPlanImage,
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await saveLayout(layout);

  // If this layout becomes active later, dining tables will be synced
  // by the setActiveLayout or activateLayout functions

  return layout;
}

// Update layout
export async function updateLayout(
  layoutId: string,
  updates: Partial<Omit<TableLayout, 'id' | 'createdAt'>>
): Promise<void> {
  await initializeLayoutService();

  const existingLayout = await getLayoutById(layoutId);
  if (!existingLayout) {
    throw new Error('Layout not found');
  }

  const updatedLayout: TableLayout = {
    ...existingLayout,
    ...updates,
    updatedAt: new Date().toISOString()
  };

  await saveLayout(updatedLayout);
}

// Update layout with tables and floor plan
export async function updateLayoutContent(
  layoutId: string,
  name: string,
  tables: LayoutTable[],
  floorPlanImage: string | null
): Promise<void> {
  await initializeLayoutService();

  const existingLayout = await getLayoutById(layoutId);
  if (!existingLayout) {
    throw new Error('Layout not found');
  }

  const updatedLayout: TableLayout = {
    ...existingLayout,
    name,
    tables,
    floorPlanImage,
    updatedAt: new Date().toISOString()
  };

  await saveLayout(updatedLayout);

  // If this layout is active, sync the dining tables
  if (updatedLayout.isActive) {
    console.log(`🔄 Layout ${name} is active, syncing dining tables...`);
    await syncDiningTablesForLayout(updatedLayout);
  }
}

// Sync dining tables with layout changes (preserves existing table status and orders)
export async function syncDiningTablesForLayout(layout: TableLayout): Promise<void> {
  // Import the table service functions we need
  const { dbService } = await import('./indexedDBService');
  const { saveDiningTables } = await import('./tableService');

  console.log(`🔄 Syncing dining tables for layout: ${layout.name}`);

  // Get existing dining tables for this layout
  const existingTables = await dbService.getByIndex<DiningTable>('diningTables', 'layoutId', layout.id);
  const existingTableMap = new Map(existingTables.map(t => [t.id, t]));

  // Get current layout table IDs (as composite IDs)
  const layoutTableIds = new Set(layout.tables.map(t => `${layout.id}-${t.id}`));

  // Remove dining tables that no longer exist in the layout
  const tablesToRemove = existingTables.filter(t => !layoutTableIds.has(t.id));
  for (const table of tablesToRemove) {
    console.log(`🗑️ Removing dining table ${table.number} (ID: ${table.id}) - no longer in layout`);
    await dbService.delete('diningTables', table.id);
  }

  // Create or update dining tables from layout
  const diningTables = layout.tables.map(layoutTable => {
    const compositeId = `${layout.id}-${layoutTable.id}`;
    const existing = existingTableMap.get(compositeId);
    return {
      id: compositeId,
      tableId: layoutTable.id,
      number: layoutTable.number,
      seats: layoutTable.seats,
      status: existing?.status || 'available',
      layoutId: layout.id,
      currentOrderId: existing?.currentOrderId,
      lastOrderTime: existing?.lastOrderTime,
      name: `${layout.name} - ${layoutTable.number}`
    };
  });

  // Save all dining tables (this will create new ones and update existing ones)
  await saveDiningTables(diningTables);

  console.log(`✅ Synced ${diningTables.length} dining tables for layout ${layout.name}`);
}

// Delete layout
export async function deleteLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();
  
  const layout = await getLayoutById(layoutId);
  if (!layout) {
    throw new Error('Layout not found');
  }
  
  if (layout.isActive) {
    throw new Error('Cannot delete active layout');
  }
  
  await dbService.delete('tableLayouts', layoutId);
}

// Set active layout (deprecated - use toggleLayoutActive for multiple layouts)
export async function setActiveLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();

  // First, set all layouts to inactive
  const allLayouts = await getAllLayouts();
  for (const layout of allLayouts) {
    if (layout.isActive) {
      await updateLayout(layout.id, { isActive: false });
    }
  }

  // Then set the specified layout as active
  await updateLayout(layoutId, { isActive: true });
}

// Toggle layout active status (supports multiple active layouts)
export async function toggleLayoutActive(layoutId: string, isActive: boolean): Promise<void> {
  await initializeLayoutService();
  await updateLayout(layoutId, { isActive });
}

// Activate layout (add to active layouts without deactivating others)
export async function activateLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();
  await updateLayout(layoutId, { isActive: true });
}

// Deactivate layout
export async function deactivateLayout(layoutId: string): Promise<void> {
  await initializeLayoutService();
  await updateLayout(layoutId, { isActive: false });
}

// Duplicate layout
export async function duplicateLayout(layoutId: string, newName: string): Promise<TableLayout> {
  await initializeLayoutService();
  
  const originalLayout = await getLayoutById(layoutId);
  if (!originalLayout) {
    throw new Error('Layout not found');
  }
  
  return await createLayout(newName, [...originalLayout.tables], originalLayout.floorPlanImage);
}

// Get layouts sorted by creation date
export async function getLayoutsSortedByDate(): Promise<TableLayout[]> {
  const layouts = await getAllLayouts();
  return layouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

// Search layouts by name
export async function searchLayoutsByName(searchTerm: string): Promise<TableLayout[]> {
  const layouts = await getAllLayouts();
  return layouts.filter(layout => 
    layout.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
}

// Get layout statistics
export async function getLayoutStatistics(): Promise<{
  totalLayouts: number;
  activeLayout: string | null;
  totalTables: number;
  averageTablesPerLayout: number;
}> {
  const layouts = await getAllLayouts();
  const activeLayout = await getActiveLayout();
  const totalTables = layouts.reduce((sum, layout) => sum + layout.tables.length, 0);
  
  return {
    totalLayouts: layouts.length,
    activeLayout: activeLayout?.name || null,
    totalTables,
    averageTablesPerLayout: layouts.length > 0 ? totalTables / layouts.length : 0
  };
}

// Validate layout
export function validateLayout(layout: Partial<TableLayout>): string[] {
  const errors: string[] = [];
  
  if (!layout.name || layout.name.trim().length === 0) {
    errors.push('Layout name is required');
  }
  
  if (!layout.tables || layout.tables.length === 0) {
    errors.push('Layout must have at least one table');
  }
  
  if (layout.tables) {
    const tableNumbers = layout.tables.map(t => t.number);
    const duplicateNumbers = tableNumbers.filter((num, index) => tableNumbers.indexOf(num) !== index);
    if (duplicateNumbers.length > 0) {
      errors.push(`Duplicate table numbers found: ${duplicateNumbers.join(', ')}`);
    }
  }
  
  return errors;
}

// Export layout data (for backup/sharing)
export async function exportLayout(layoutId: string): Promise<string> {
  const layout = await getLayoutById(layoutId);
  if (!layout) {
    throw new Error('Layout not found');
  }
  
  return JSON.stringify(layout, null, 2);
}

// Import layout data
export async function importLayout(layoutData: string, newName?: string): Promise<TableLayout> {
  try {
    const parsedLayout = JSON.parse(layoutData) as TableLayout;
    
    // Validate the imported layout
    const errors = validateLayout(parsedLayout);
    if (errors.length > 0) {
      throw new Error(`Invalid layout data: ${errors.join(', ')}`);
    }
    
    // Create new layout with imported data
    return await createLayout(
      newName || `${parsedLayout.name} (Imported)`,
      parsedLayout.tables,
      parsedLayout.floorPlanImage
    );
  } catch (error) {
    throw new Error(`Failed to import layout: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

