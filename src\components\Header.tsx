import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { logout } from '../services/authService';


export function Header({ user, onLogout }: { user: { username: string; role: string } | null; onLogout: () => void }) {
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);

    try {
      const result = await logout();

      if (result.success) {
        // Call the parent component's logout handler
        onLogout();
      } else {
        console.error('Logout failed:', result.message);
        alert(result.message || 'Logout failed. Please try again.');
      }
    } catch (error) {
      console.error('Logout error:', error);
      alert('An error occurred during logout. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <header className="bg-blue-600 text-white p-4 shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <h1 className="text-2xl font-bold">POS System</h1>
        <nav className="flex space-x-4">
          {user ? (
            <>
              <Link to="/" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                POS
              </Link>
              {/* {user.role === 'admin' && ( */}
              <Link to="/orders" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Order Management
              </Link>
              {/* )} */}
              <Link to="/table-manager" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Table Manager
              </Link>
              <Link to="/table-viewer" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Table Viewer
              </Link>
              <Link to="/invoices" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Invoices
              </Link>
              <Link to="/profile" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
                Profile
              </Link>
              <button
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
              >
                {isLoggingOut ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Logging out...
                  </>
                ) : (
                  'Logout'
                )}
              </button>
            </>
          ) : (
            <Link to="/login" className="bg-blue-700 px-4 py-2 rounded-md hover:bg-blue-800">
              Login
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
}